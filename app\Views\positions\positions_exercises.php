<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">Position Exercises</h5>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table id="exercisesTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Exercise Name</th>
                            <th>Gazzetted No</th>
                            <th>Advertisement No</th>
                            <th>Publish Date</th>
                            <th>Groups</th>
                            <th>Positions</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    if (typeof jQuery != 'undefined') {
        initializePositionExercises();
    } else {
        console.error('jQuery is not loaded');
    }
});

function initializePositionExercises() {
    // Initialize DataTable
    const table = $('#exercisesTable').DataTable({
        ajax: {
            url: '<?= base_url('positions/exercises_list') ?>',
            dataSrc: function(json) {
                return json.data;
            }
        },
        columns: [
            { data: 'exercise_name' },
            { data: 'gazzetted_no' },
            { data: 'advertisement_no' },
            { data: 'publish_date_from' },
            {
                data: 'group_count',
                render: function(data) {
                    return `<span class="badge bg-info">${data}</span>`;
                }
            },
            {
                data: 'position_count',
                render: function(data) {
                    return `<span class="badge bg-primary">${data}</span>`;
                }
            },
            {
                data: 'status',
                render: function(data) {
                    const statusClasses = {
                        'draft': 'bg-secondary',
                        'published': 'bg-success',
                        'selection': 'bg-info',
                        'archived': 'bg-danger'
                    };
                    const displayStatus = data.charAt(0).toUpperCase() + data.slice(1);
                    return `<span class="badge ${statusClasses[data] || 'bg-secondary'} text-white" style="font-weight: 500; padding: 0.35em 0.65em;">${displayStatus}</span>`;
                }
            },
            {
                data: null,
                render: function(data) {
                    return `<a href="<?= base_url('positions/positions_groups') ?>/${data.id}" class="btn btn-sm btn-primary" title="View Position Groups">
                                <i class="fas fa-eye"></i> View
                            </a>`;
                }
            }
        ]
    });
}
</script>
<?= $this->endSection() ?>