<?php namespace App\Models;

use CodeIgniter\Model;

/**
 * UsersModel
 *
 * Model for the users table
 */
class UsersModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    protected $allowedFields = [
        'org_id',
        'orgcode',
        'fileno',
        'name',
        'username',
        'password',
        'role',
        'position',
        'id_photo',
        'phone',
        'email',
        'status',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'org_id' => 'required|numeric',
        'orgcode' => 'required|numeric',
        'fileno' => 'required|max_length[100]',
        'name' => 'required|max_length[255]',
        'username' => 'required|max_length[255]|is_unique[users.username,id,{id}]',
        'password' => 'required|min_length[6]',
        'role' => 'required|in_list[admin,supervisor,user,guest]',
        'id_photo' => 'required|max_length[500]',
        'phone' => 'required|max_length[200]',
        'email' => 'required|max_length[500]|valid_email',
        'status' => 'required|max_length[20]'
    ];

    protected $validationMessages = [
        'username' => [
            'is_unique' => 'Username already exists'
        ],
        'role' => [
            'in_list' => 'Role must be one of: admin, supervisor, user, guest'
        ],
        'email' => [
            'valid_email' => 'Please provide a valid email address'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    protected function hashPassword(array $data)
    {
        if (!isset($data['data']['password'])) {
            return $data;
        }

        $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        return $data;
    }

    // Custom Methods
    public function findByUsername($username)
    {
        return $this->where('username', $username)
                    ->first();
    }

    public function getActiveUsers()
    {
        return $this->where('status', 'active')
                    ->findAll();
    }

    public function getUsersByRole($role)
    {
        return $this->where('role', $role)
                    ->findAll();
    }

    /**
     * Get users by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getUsersByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)->findAll();
    }

    /**
     * Get users by organization code
     *
     * @param int $orgcode
     * @return array
     */
    public function getUsersByOrgCode($orgcode)
    {
        return $this->where('orgcode', $orgcode)->findAll();
    }

    /**
     * Verify user credentials
     *
     * @param string $username
     * @param string $password
     * @return array|null
     */
    public function verifyUser($username, $password)
    {
        $user = $this->findByUsername($username);

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }

        return null;
    }

    /**
     * Get user statistics
     *
     * @return array
     */
    public function getUserStatistics()
    {
        $stats = [];

        // Total users
        $stats['total'] = $this->countAllResults(false);

        // Active users
        $stats['active'] = $this->where('status', '1')->countAllResults(false);

        // Users by role
        $roleStats = $this->select('role, COUNT(*) as count')
                          ->groupBy('role')
                          ->findAll();

        $stats['by_role'] = [];
        foreach ($roleStats as $stat) {
            $stats['by_role'][$stat['role']] = $stat['count'];
        }

        return $stats;
    }

    /**
     * Search users by name or username
     *
     * @param string $search
     * @return array
     */
    public function searchUsers($search)
    {
        return $this->groupStart()
                    ->like('name', $search)
                    ->orLike('username', $search)
                    ->groupEnd()
                    ->findAll();
    }
    /**
     * Get validation rules for admin updates
     */
    public function getAdminUpdateValidationRules($id = null)
    {
        return [
            'name' => 'required|max_length[255]',
            'username' => 'required|max_length[255]|is_unique[users.username,id,' . $id . ']',
            'password' => 'permit_empty|min_length[6]',
            'role' => 'required|in_list[admin,supervisor,user,guest]',
            'org_id' => 'permit_empty|numeric',
            'orgcode' => 'permit_empty|max_length[100]',
            'status' => 'permit_empty|in_list[0,1,active,inactive]'
        ];
    }

    /**
     * Get validation rules for admin creation
     */
    public function getAdminCreateValidationRules()
    {
        return [
            'name' => 'required|max_length[255]',
            'username' => 'required|max_length[255]|is_unique[users.username]',
            'password' => 'required|min_length[6]',
            'role' => 'required|in_list[admin,supervisor,user,guest]',
            'org_id' => 'permit_empty|numeric',
            'orgcode' => 'permit_empty|max_length[100]',
            'status' => 'permit_empty|in_list[0,1,active,inactive]'
        ];
    }
}
